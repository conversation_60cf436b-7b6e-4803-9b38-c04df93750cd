# SRS WebRTC简化配置文件
# 专为解决推流问题优化

# 基础服务配置
listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
srs_log_level       info;

# HTTP服务器配置
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;

    # 暂时禁用HTTPS以避免SSL问题
    # https {
    #     enabled     off;
    #     listen      8443;
    #     key         ./conf/server.key;
    #     cert        ./conf/server.crt;
    # }
}

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;

    # 暂时禁用HTTPS API
    # https {
    #     enabled     off;
    #     listen      1990;
    #     key         ./conf/server.key;
    #     cert        ./conf/server.crt;
    # }
}

# 统计信息配置
stats {
    network         0;
}

# WebRTC服务器配置 - 简化版本
rtc_server {
    enabled         on;
    listen          8000;
    candidate       **************;
    
    # 仅使用UDP协议
    protocol        udp;
    
    # 简化网络配置
    use_auto_detect_network_ip  off;
    ip_family       ipv4;
    api_as_candidates on;
    
    # 基础安全配置
    ecdsa           on;
    encrypt         on;
}

# 虚拟主机配置 - 简化版本
vhost __defaultVhost__ {
    # WebRTC配置 - 最小化设置
    rtc {
        enabled         on;
        rtmp_to_rtc     on;
        rtc_to_rtmp     on;
        
        # 禁用复杂的网络优化功能
        nack            off;
        twcc            off;
        
        # 基础超时设置
        stun_timeout    30;
        stun_strict_check off;
        
        # DTLS配置
        dtls_role       passive;
        dtls_version    auto;
        
        # 编解码配置
        opus_bitrate    48000;
        aac_bitrate     48000;
        
        # 视频配置
        keep_bframe     off;
        keep_avc_nalu_sei on;
    }
    
    # HTTP流媒体重混配置
    http_remux {
        enabled         on;
        mount           [vhost]/[app]/[stream].flv;
    }
    
    # HLS配置
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
    }
    
    # 播放配置
    play {
        gop_cache       on;
        gop_cache_max_frames 2500;
        queue_length    10;
        mw_latency      100;
    }
    
    # 推流配置
    publish {
        parse_sps       on;
        mr              off;
        normal_timeout  5000;
    }
    
    # 启用最小延迟模式
    min_latency     on;
    
    # 禁用安全检查
    security {
        enabled         off;
    }
}

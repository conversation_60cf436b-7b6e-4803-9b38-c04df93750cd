#!/bin/bash

# SRS WebRTC局域网推流服务器启动脚本
# 服务器IP: **************

echo "=========================================="
echo "SRS WebRTC局域网推流服务器启动脚本"
echo "服务器IP: **************"
echo "=========================================="

# 检查SRS是否已编译
if [ ! -f "srs/trunk/objs/srs" ]; then
    echo "错误: SRS未编译，请先编译SRS"
    echo "编译命令:"
    echo "  cd srs/trunk"
    echo "  ./configure --rtc=on --https=on"
    echo "  make"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "srs/trunk/conf/webrtc_simple.conf" ]; then
    echo "错误: 配置文件不存在: srs/trunk/conf/webrtc_simple.conf"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        echo "警告: 端口 $port ($service) 已被占用"
        echo "请检查是否有其他SRS实例正在运行"
        return 1
    fi
    return 0
}

echo "检查端口占用情况..."
check_port 1935 "RTMP"
check_port 8000 "WebRTC"
check_port 1985 "HTTP-API"
check_port 8080 "HTTP"
check_port 8443 "HTTPS"

# 检查SSL证书
if [ ! -f "srs/trunk/conf/server.key" ] || [ ! -f "srs/trunk/conf/server.crt" ]; then
    echo "警告: SSL证书文件不存在，HTTPS功能可能无法正常工作"
    echo "证书文件位置:"
    echo "  - srs/trunk/conf/server.key"
    echo "  - srs/trunk/conf/server.crt"
fi

# 设置环境变量
export CANDIDATE=**************

# 进入SRS目录
cd srs/trunk

echo ""
echo "启动SRS服务器..."
echo "配置文件: conf/webrtc_simple.conf"
echo "服务器IP: $CANDIDATE"
echo ""
echo "服务端口:"
echo "  - RTMP推流: 1935"
echo "  - WebRTC: 8000 (UDP+TCP)"
echo "  - HTTP API: 1985"
echo "  - HTTP服务器: 8080"
echo "  - HTTPS服务器: 8443"
echo ""
echo "🔒 推荐使用HTTPS推流页面: https://**************:8443/webrtc_publisher_https.html"
echo "📄 HTTP推流页面: http://**************:8080/webrtc_publisher.html"
echo "🎛️ SRS管理页面: http://**************:8080/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================================="

# 启动SRS服务器
./objs/srs -c conf/webrtc_simple.conf

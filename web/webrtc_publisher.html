<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SRS WebRTC推流测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .video-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .video-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .video-container h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.3em;
        }
        
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .controls {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .control-group input,
        .control-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 120px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }
        
        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 600;
            color: #333;
        }
        
        .status-value {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status-success {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-warning {
            color: #ffc107;
            font-weight: 600;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: 600;
        }
        
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }
        
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #1976D2;
        }
        
        .info-box p {
            margin: 5px 0;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .video-section {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 SRS WebRTC推流测试</h1>
            <p>局域网环境 WebRTC 推流到 SRS 服务器</p>
        </div>
        
        <div class="content">
            <div class="info-box">
                <h4>📋 使用说明</h4>
                <p>1. 确保SRS服务器已启动 (**************)</p>
                <p>2. 授权浏览器访问摄像头和麦克风</p>
                <p>3. 配置推流参数并点击"开始推流"</p>
                <p>4. 推流成功后可在播放端观看: <code>webrtc://**************:8000/live/livestream</code></p>
            </div>
            
            <div class="video-section">
                <div class="video-container">
                    <h3>📹 本地预览</h3>
                    <video id="localVideo" autoplay muted playsinline></video>
                </div>
                <div class="video-container">
                    <h3>📊 推流状态</h3>
                    <div class="status">
                        <div class="status-item">
                            <span class="status-label">状态:</span>
                            <span id="status" class="status-value">未连接</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">会话ID:</span>
                            <span id="sessionId" class="status-value">-</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">推流时长:</span>
                            <span id="duration" class="status-value">00:00:00</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">视频编码:</span>
                            <span id="videoCodec" class="status-value">-</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">音频编码:</span>
                            <span id="audioCodec" class="status-value">-</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <label for="streamUrl">推流地址:</label>
                    <input type="text" id="streamUrl" value="webrtc://**************:8000/live/livestream" readonly>
                </div>
                
                <div class="control-group">
                    <label for="videoQuality">视频质量:</label>
                    <select id="videoQuality">
                        <option value="640x480">640x480 (标清)</option>
                        <option value="1280x720" selected>1280x720 (高清)</option>
                        <option value="1920x1080">1920x1080 (全高清)</option>
                    </select>
                </div>
                
                <div class="button-group">
                    <button id="startBtn" class="btn btn-primary">🚀 开始推流</button>
                    <button id="stopBtn" class="btn btn-danger" disabled>⏹️ 停止推流</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入SRS SDK -->
    <script src="http://**************:8080/players/js/adapter-7.4.0.min.js"></script>
    <script src="http://**************:8080/players/js/srs.sdk.js"></script>

    <script>
        // 全局变量
        let sdk = null;
        let isPublishing = false;
        let startTime = null;
        let durationTimer = null;

        // DOM元素
        const localVideo = document.getElementById('localVideo');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const streamUrl = document.getElementById('streamUrl');
        const videoQuality = document.getElementById('videoQuality');
        const status = document.getElementById('status');
        const sessionId = document.getElementById('sessionId');
        const duration = document.getElementById('duration');
        const videoCodec = document.getElementById('videoCodec');
        const audioCodec = document.getElementById('audioCodec');

        // 事件监听器
        startBtn.addEventListener('click', startPublish);
        stopBtn.addEventListener('click', stopPublish);

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            console.log('页面加载完成，初始化WebRTC推流页面');
            updateStatus('🔄 准备就绪', 'status-warning');
        });

        // 更新状态显示
        function updateStatus(text, className) {
            status.textContent = text;
            status.className = 'status-value ' + className;
        }

        // 更新推流时长
        function updateDuration() {
            if (!startTime) return;

            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);

            duration.textContent =
                String(hours).padStart(2, '0') + ':' +
                String(minutes).padStart(2, '0') + ':' +
                String(seconds).padStart(2, '0');
        }

        // 重置状态
        function resetState() {
            isPublishing = false;
            startTime = null;

            if (durationTimer) {
                clearInterval(durationTimer);
                durationTimer = null;
            }

            duration.textContent = '00:00:00';
            sessionId.textContent = '-';
            videoCodec.textContent = '-';
            audioCodec.textContent = '-';

            startBtn.disabled = false;
            stopBtn.disabled = true;

            // 清理本地视频流
            if (localVideo.srcObject) {
                const tracks = localVideo.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                localVideo.srcObject = null;
            }
        }

        // 获取视频约束
        function getVideoConstraints() {
            const quality = videoQuality.value;
            const [width, height] = quality.split('x').map(Number);

            return {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: {
                    width: { ideal: width, max: width },
                    height: { ideal: height, max: height },
                    frameRate: { ideal: 30, max: 30 }
                }
            };
        }

        // 开始推流
        async function startPublish() {
            try {
                updateStatus('🔄 正在连接...', 'status-warning');
                startBtn.disabled = true;

                // 创建SDK实例
                if (sdk) {
                    sdk.close();
                }
                sdk = new SrsRtcPublisherAsync();

                // 设置约束
                sdk.constraints = getVideoConstraints();

                // 设置本地视频流
                localVideo.srcObject = sdk.stream;

                // 设置事件监听器
                sdk.ontrack = function(event) {
                    console.log('收到远程轨道:', event);
                };

                sdk.pc.oniceconnectionstatechange = function() {
                    console.log('ICE连接状态:', sdk.pc.iceConnectionState);
                };

                sdk.pc.onconnectionstatechange = function() {
                    console.log('连接状态:', sdk.pc.connectionState);

                    if (sdk.pc.connectionState === 'connected') {
                        // 获取编解码器信息
                        try {
                            const stats = sdk.pc.getStats();
                            stats.then(reports => {
                                reports.forEach(report => {
                                    if (report.type === 'outbound-rtp') {
                                        if (report.mediaType === 'video') {
                                            videoCodec.textContent = report.codecId || 'H.264';
                                        } else if (report.mediaType === 'audio') {
                                            audioCodec.textContent = report.codecId || 'opus';
                                        }
                                    }
                                });
                            });
                        } catch (e) {
                            console.warn('获取编解码器信息失败:', e);
                            audioCodec.textContent = 'opus';
                            videoCodec.textContent = 'H.264';
                        }
                    }
                };

                // 开始推流
                const url = streamUrl.value;
                const session = await sdk.publish(url);

                // 推流成功
                isPublishing = true;
                startTime = Date.now();
                updateStatus('推流中', 'status-success');
                sessionId.textContent = session.sessionid || '已连接';

                // 启动时长计时器
                durationTimer = setInterval(updateDuration, 1000);

                // 更新按钮状态
                startBtn.disabled = true;
                stopBtn.disabled = false;

                console.log('推流成功:', session);

            } catch (error) {
                console.error('推流失败:', error);
                updateStatus('❌ 推流失败', 'status-error');

                let errorMsg = '推流失败';
                if (error.name === 'HttpsRequiredError') {
                    errorMsg = 'HTTPS环境要求错误';
                } else if (error.name === 'NotAllowedError') {
                    errorMsg = '用户拒绝摄像头/麦克风权限';
                } else if (error.name === 'NotFoundError') {
                    errorMsg = '未找到摄像头或麦克风设备';
                } else if (error.message) {
                    errorMsg = error.message;
                }

                alert(`推流失败: ${errorMsg}\n\n请确保:\n1. 已授权摄像头和麦克风权限\n2. 设备正常工作\n3. 网络连接正常`);

                // 重置状态
                resetState();
            }
        }

        // 停止推流
        function stopPublish() {
            try {
                if (sdk) {
                    sdk.close();
                    sdk = null;
                }

                updateStatus('⏹️ 已停止', 'status-warning');
                resetState();

                console.log('推流已停止');

            } catch (error) {
                console.error('停止推流失败:', error);
            }
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (sdk) {
                sdk.close();
            }
        });
    </script>
</body>
</html>
